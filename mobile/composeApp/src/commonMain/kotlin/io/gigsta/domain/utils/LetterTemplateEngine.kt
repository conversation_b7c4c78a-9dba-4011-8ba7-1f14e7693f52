package io.gigsta.domain.utils

import co.touchlab.kermit.Logger
import com.github.mustachejava.DefaultMustacheFactory
import java.io.StringReader
import java.io.StringWriter

/**
 * Mobile letter template engine using <PERSON><PERSON>'s Mustache (mustache.java)
 * Kotlin equivalent of web's fillLetterTemplate function
 * Generates design HTML from structured data using Mustache templates
 */

/**
 * Fill letter template with structured data using Ktor's Mustache engine
 * Mobile equivalent of web's fillLetterTemplate function
 */
fun fillLetterTemplate(template: LetterTemplate, data: StructuredLetterData): String {
    return try {
        Logger.d { "Filling template ${template.name} with Ktor Mustache engine" }

        // Convert structured data to template format
        val templateData = convertToLetterTemplateData(data)

        // Get template HTML string
        val templateHtml = getTemplateHtml(template.id)

        // Create Mustache factory and compile template
        val mustacheFactory = DefaultMustacheFactory()
        val mustache = mustacheFactory.compile(StringReader(templateHtml), template.id)

        // Create context map for Mustache
        val context = createMustacheContext(templateData)

        // Render template
        val writer = StringWriter()
        mustache.execute(writer, context)
        val result = writer.toString()

        Logger.d { "Successfully rendered template ${template.name}" }
        result
    } catch (error: Exception) {
        Logger.e(error) { "Error filling template ${template.name}: ${error.message}" }
        throw Exception("Failed to fill letter template \"${template.name}\": ${error.message}")
    }
}

/**
 * Create Mustache context from template data
 */
private fun createMustacheContext(data: LetterTemplateData): Map<String, Any?> {
    return mapOf(
        "date" to data.date,
        "subject" to data.subject,
        "salutation" to data.salutation,
        "opening" to data.opening,
        "farewell" to data.farewell,
        "signatureName" to data.signatureName,

        // Handle recipient lines as a list for {{#recipientLines}}
        "recipientLines" to data.recipientLines,

        // Handle paragraphs as a list for {{#paragraphs}}
        "paragraphs" to data.paragraphs,

        // Handle conditional fields - for Mustache, use the value itself for conditionals
        // If closing exists and is not empty, it will render; otherwise it won't
        "closing" to if (!data.closing.isNullOrBlank()) data.closing else false,

        // Same for additionalInfo
        "additionalInfo" to if (!data.additionalInfo.isNullOrBlank()) data.additionalInfo else false
    )
}

/**
 * Test function to verify Ktor Mustache is working properly
 */
fun testMustacheTemplateEngine(): String {
    return try {
        val testTemplate = "Hello {{name}}! {{#items}}Item: {{.}} {{/items}}"
        val testContext = mapOf(
            "name" to "World",
            "items" to listOf("A", "B", "C")
        )

        val mustacheFactory = DefaultMustacheFactory()
        val mustache = mustacheFactory.compile(StringReader(testTemplate), "test")
        val writer = StringWriter()
        mustache.execute(writer, testContext)
        val result = writer.toString()

        Logger.d { "Mustache test result: $result" }
        result
    } catch (error: Exception) {
        Logger.e(error) { "Mustache test failed: ${error.message}" }
        "Mustache test failed: ${error.message}"
    }
}

/**
 * Get template HTML string by template ID
 */
private fun getTemplateHtml(templateId: String): String {
    return when (templateId) {
        "plain-text" -> PLAIN_TEXT_TEMPLATE
        "classic-blue" -> CLASSIC_BLUE_TEMPLATE
        "professional-classic" -> PROFESSIONAL_CLASSIC_TEMPLATE
        "minimalist-sidebar" -> MINIMALIST_SIDEBAR_TEMPLATE
        "minimalist-border-frame" -> MINIMALIST_BORDER_FRAME_TEMPLATE
        "minimalist-accent" -> MINIMALIST_ACCENT_TEMPLATE
        "minimalist-circular-accents" -> MINIMALIST_CIRCULAR_ACCENTS_TEMPLATE
        else -> PLAIN_TEXT_TEMPLATE // Fallback to plain text
    }
}









// Template HTML strings (converted from Handlebars templates)
private const val PLAIN_TEXT_TEMPLATE = """<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Surat Lamaran Kerja</title>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        @media print {
            body { print-color-adjust: exact; }
            .no-print { display: none; }
        }
        
        .a4-page {
            width: 210mm;
            height: 297mm;
            margin: 0 auto;
            background: white;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }
        
        @page {
            size: A4;
            margin: 0;
        }
        
        body {
            font-family: Roboto, sans-serif;
            line-height: 1.5;
            font-size: 15px;            
        }
        
        .content-area {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            padding: 40px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .header h1 {
            margin: 0;
        }
        
        .date {
            text-align: right;
            margin-bottom: 30px;
        }
        
        .subject {
            margin-bottom: 20px;
        }
        
        .salutation {
            margin-bottom: 20px;
        }
        
        .body-text {
            margin-bottom: 20px;
            text-align: justify;
        }
        
        .closing {
            margin-top: 40px;
        }
        
        .signature-space {
            margin: 60px 0 20px 0;
        }
    </style>
</head>
<body>
    <div class="a4-page">
        <div class="content-area">
            <div>
                <div class="header font-bold text-2xl tracking-wider">
                    <h1>SURAT LAMARAN KERJA</h1>
                </div>

                <div class="date">
                    <p>{{date}}</p>
                </div>

                <div class="subject">
                    <p>{{subject}}</p>
                </div>

                <div class="salutation">
                    <p>
                        {{#recipientLines}}
                        <span>{{.}}</span><br>
                        {{/recipientLines}}
                    </p>
                </div>

                <div class="body-text">
                    <p>{{opening}}</p>
                </div>

                {{#paragraphs}}
                <div class="body-text">
                    <p>{{.}}</p>
                </div>
                {{/paragraphs}}

                {{#closing}}
                <div class="body-text">
                    <p>{{.}}</p>
                </div>
                {{/closing}}
            </div>

            <div>
                <div class="closing">
                    <p>{{farewell}}</p>
                    <div class="signature-space"></div>
                    <p>{{signatureName}}</p>
                    {{#additionalInfo}}
                    <p>{{.}}</p>
                    {{/additionalInfo}}
                </div>
            </div>
        </div>
    </div>
</body>
</html>"""

private const val PROFESSIONAL_CLASSIC_TEMPLATE = """<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Surat Lamaran Kerja</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            line-height: 1;
            font-size: 14px;
        }
        .a4-page {
            width: 210mm;
            height: 297mm;
            display: flex;
            flex-direction: column;
        }
        .content-body {
            flex-grow: 1;
        }
    </style>
</head>
<body>
    <div class="a4-page max-w-4xl mx-auto bg-white">
        <!-- Header -->
        <div class="bg-blue-800 text-white py-6 px-8">
            <h1 class="text-2xl font-bold text-center tracking-wide">SURAT LAMARAN KERJA</h1>
        </div>

        <!-- Date and Header Info -->
        <div class="px-8 pt-8">
            <div class="text-right mb-4">
                <p class="font-medium text-gray-800">{{date}}</p>
            </div>

            <div class="mb-4">
                <p class="font-semibold text-gray-800 mb-4">{{subject}}</p>
                <p class="leading-relaxed text-gray-700">
                    {{#recipientLines}}
                    {{.}}<br>
                    {{/recipientLines}}
                </p>
            </div>
        </div>

        <!-- Content Body -->
        <div class="content-body px-8">
            <div class="space-y-4 leading-loose text-justify text-gray-700">
                <p>{{opening}}</p>

                {{#paragraphs}}
                <p>{{.}}</p>
                {{/paragraphs}}

                {{#closing}}
                <p>{{.}}</p>
                {{/closing}}
            </div>
        </div>

        <!-- Signature Section - Fixed at bottom -->
        <div class="px-8 pb-4 mt-auto">
            <div class="pt-6">
                <p class="font-semibold text-gray-800 mb-16">{{farewell}}</p>
                <div>
                    <p class="font-bold text-gray-800 border-b-2 border-blue-800 inline-block pb-1">{{signatureName}}</p>
                    {{#additionalInfo}}
                    <p class="text-gray-700 mt-2">{{.}}</p>
                    {{/additionalInfo}}
                </div>
            </div>
        </div>

        <!-- Footer with spacing -->
        <div class="bg-blue-800 h-4 mt-4"></div>
    </div>
</body>
</html>"""

private const val CLASSIC_BLUE_TEMPLATE = """<!DOCTYPE html>
<html lang="id">
<head>
   <meta charset="UTF-8">
   <meta name="viewport" content="width=device-width, initial-scale=1.0">
   <title>Surat Lamaran Kerja</title>
   <script src="https://cdn.tailwindcss.com"></script>
   <link href="https://fonts.googleapis.com/css2?family=Merriweather:wght@400;500;600;700&display=swap" rel="stylesheet">
   <style>
       @media print {
           body { print-color-adjust: exact; }
           .no-print { display: none; }
       }

       .a4-page {
           width: 210mm;
           height: 297mm;
           margin: 0 auto;
           background: white;
           overflow: hidden;
           display: flex;
           flex-direction: column;
       }

       @page {
           size: A4;
           margin: 0;
       }

       body {
           font-family: 'Merriweather', serif;
           line-height: 1.5;
           font-size: 15px;
       }

       .content-area {
           flex: 1;
           display: flex;
           flex-direction: column;
           justify-content: space-between;
       }
   </style>
</head>
<body>
   <div class="a4-page p-8">
       <div class="content-area">
           <div>
               <div class="border-b-2 border-blue-600 pb-4 mb-8">
                   <div class="flex items-center justify-between">
                       <div class="w-16 h-1 bg-blue-600"></div>
                       <h1 class="text-2xl font-bold text-blue-900 tracking-wider">SURAT LAMARAN KERJA</h1>
                       <div class="w-16 h-1 bg-blue-600"></div>
                   </div>
               </div>

               <div class="text-right mb-8">
                   <p class="text-gray-700 font-medium">{{date}}</p>
               </div>

               <div class="mb-6">
                   <p class="font-semibold text-gray-800">
                       <span>{{subject}}</span>
                   </p>
               </div>

               <div class="mb-8">
                   <p class="text-gray-800">
                       {{#recipientLines}}
                       <span>{{.}}</span><br>
                       {{/recipientLines}}
                   </p>
               </div>

               <div class="mb-6">
                   <p>{{opening}}</p>
               </div>

               <div class="space-y-5 text-justify text-gray-800 leading-relaxed">
                   {{#paragraphs}}
                   <p>{{.}}</p>
                   {{/paragraphs}}

                   {{#closing}}
                   <p>{{.}}</p>
                   {{/closing}}
               </div>
           </div>

           <div>
               <div class="mt-10 mb-8">
                   <p class="text-gray-800 font-medium">{{farewell}}</p>

                   <div class="mt-16 mb-4">
                       <div class="w-48 border-b border-gray-400"></div>
                   </div>

                   <p class="font-bold text-gray-900">{{signatureName}}</p>
                   {{#additionalInfo}}
                   <p class="text-gray-700">{{.}}</p>
                   {{/additionalInfo}}
               </div>

               <div class="pt-4 border-t border-gray-300">
                   <div class="flex justify-center">
                       <div class="flex space-x-2">
                           <div class="w-2 h-2 bg-blue-600 rounded-full"></div>
                           <div class="w-2 h-2 bg-blue-400 rounded-full"></div>
                           <div class="w-2 h-2 bg-blue-300 rounded-full"></div>
                       </div>
                   </div>
               </div>
           </div>
       </div>
   </div>
</body>
</html>"""

private const val MINIMALIST_SIDEBAR_TEMPLATE = """<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Surat Lamaran Kerja</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Source+Sans+Pro:wght@400;500;600&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Source Sans Pro', sans-serif; }
        .a4-page { height: 297mm; width: 210mm; margin: 0 auto; overflow: hidden; }
        .letter-content { height: 100%; display: flex; font-size: 15px; line-height: 1.6; }
        .main-content { flex-grow: 1; display: flex; flex-direction: column; }
        .content-body { flex-grow: 1; }
        @media print { .a4-page { margin: 0; } }
    </style>
</head>
<body class="bg-white m-0 p-0">
    <div class="a4-page bg-white">
        <div class="letter-content">
            <!-- Left Sidebar -->
            <div class="w-4 bg-indigo-600"></div>

            <!-- Main Content -->
            <div class="main-content">
                <!-- Header -->
                <div class="bg-gray-50 border-b border-gray-200 px-8 py-5">
                    <h1 class="text-lg font-semibold text-gray-800 tracking-wide">SURAT LAMARAN KERJA</h1>
                </div>

                <!-- Date -->
                <div class="px-8 pt-6 pb-2">
                    <div class="text-right">
                        <span class="bg-indigo-50 text-indigo-800 px-3 py-1 rounded text-sm font-medium">{{date}}</span>
                    </div>
                </div>

                <!-- Header Info -->
                <div class="px-8 pb-4">
                    <div class="border-l-2 border-indigo-200 pl-4 mb-4">
                        <p class="font-medium text-gray-800 mb-1">{{subject}}</p>
                    </div>

                    <div class="text-gray-700 text-sm">
                        {{#recipientLines}}
                        <p>{{.}}</p>
                        {{/recipientLines}}
                    </div>
                </div>

                <!-- Content Body -->
                <div class="content-body px-8">
                    <div class="space-y-4 text-justify text-gray-700">
                        <p>{{opening}}</p>

                        {{#paragraphs}}
                        <p>{{.}}</p>
                        {{/paragraphs}}

                        {{#closing}}
                        <p>{{.}}</p>
                        {{/closing}}
                    </div>
                </div>

                <!-- Signature Section - Fixed at bottom -->
                <div class="px-8 pb-8 mt-auto">
                    <div class="pt-6">
                        <p class="font-medium text-gray-800 mb-16">{{farewell}}</p>
                        <div>
                            <p class="font-medium text-gray-800">{{signatureName}}</p>
                            {{#additionalInfo}}
                            <p class="text-gray-600 text-sm mt-1">{{.}}</p>
                            {{/additionalInfo}}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>"""

private const val MINIMALIST_BORDER_FRAME_TEMPLATE = """<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Surat Lamaran Kerja</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Lato:wght@400;500;600&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Lato', sans-serif;
        }
        .a4-page {
            height: 297mm;
            width: 210mm;
            margin: 0 auto;
            overflow: hidden;
        }
        .letter-content {
            height: 100%;
            display: flex;
            flex-direction: column;
            font-size: 15px;
            line-height: 1.6;
            position: relative;
        }
        .content-body {
            flex-grow: 1;
        }
        .border-l-3 {
            border-left-width: 3px;
        }
        .signature-section {
            position: absolute;
            bottom: 48px;
            left: 32px;
            right: 32px;
        }
        .bottom-decoration {
            position: absolute;
            bottom: 24px;
            right: 32px;
        }
        @media print {
            .a4-page {
                margin: 0;
            }
        }
    </style>
</head>
<body class="bg-white m-0 p-0">
    <div class="a4-page bg-white">
        <div class="letter-content">
            <!-- Header -->
            <div class="border-b border-gray-200 px-8 py-6">
                <div class="relative">
                    <div class="absolute top-0 left-0 w-6 h-6 border-t-2 border-l-2 border-green-500"></div>
                    <div class="absolute top-0 right-0 w-6 h-6 border-t-2 border-r-2 border-green-500"></div>
                    <h1 class="text-lg font-medium text-center text-gray-800 py-2">SURAT LAMARAN KERJA</h1>
                </div>
            </div>

            <!-- Date and Subject Section -->
            <div class="px-8 pt-6 pb-4">
                <div class="grid grid-cols-3 gap-4 mb-6">
                    <div class="col-span-2">
                        <div class="border-l-3 border-green-400 pl-3">
                            <p class="text-gray-700 text-sm">{{subject}}</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <div class="inline-block border border-green-300 px-3 py-1 rounded">
                            <p class="text-green-700 font-medium text-sm">{{date}}</p>
                        </div>
                    </div>
                </div>

                <div class="text-gray-700 text-sm">
                    {{#recipientLines}}
                    <p>{{.}}</p>
                    {{/recipientLines}}
                </div>
            </div>

            <!-- Content Body -->
            <div class="content-body px-8 pb-32">
                <div class="space-y-4 text-justify text-gray-700">
                    <p>{{opening}}</p>

                    {{#paragraphs}}
                    <p>{{.}}</p>
                    {{/paragraphs}}

                    {{#closing}}
                    <p>{{.}}</p>
                    {{/closing}}
                </div>
            </div>

            <!-- Signature Section - Fixed at Bottom -->
            <div class="signature-section">
                <div class="mt-8 pt-4">
                    <div class="mb-12">
                        <p class="font-medium text-gray-800">{{farewell}}</p>
                    </div>
                    <div class="relative">
                        <p class="font-medium text-gray-800">{{signatureName}}</p>
                        <div class="w-32 h-px bg-green-400 mt-1"></div>
                        {{#additionalInfo}}
                        <p class="text-gray-600 text-sm mt-2">{{.}}</p>
                        {{/additionalInfo}}
                    </div>
                </div>
            </div>

            <!-- Bottom Decoration - Separate from signature -->
            <div class="bottom-decoration">
                <div class="w-4 h-4 border-b-2 border-r-2 border-green-400"></div>
            </div>
        </div>
    </div>
</body>
</html>"""

private const val MINIMALIST_ACCENT_TEMPLATE = """<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Surat Lamaran Kerja</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=IBM+Plex+Sans:wght@400;500;600&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'IBM Plex Sans', sans-serif; }
        .a4-page { height: 297mm; width: 210mm; margin: 0 auto; overflow: hidden; }
        .letter-content { height: 100%; display: flex; flex-direction: column; font-size: 14px; line-height: 1.6; position: relative; }
        .content-body { flex-grow: 1; }
        .letter-spacing-wide { letter-spacing: 0.05em; }
        .space-y-5 > * + * { margin-top: 1.25rem; }
        .signature-section { position: absolute; bottom: 60px; left: 40px; right: 40px; }
        .bottom-decoration { position: absolute; bottom: 32px; right: 40px; }
        @media print { .a4-page { margin: 0; } }
    </style>
</head>
<body class="bg-white m-0 p-0">
    <div class="a4-page bg-white">
        <div class="letter-content">
            <!-- Minimal Header -->
            <div class="px-10 pt-8 pb-6">
                <div class="text-center mb-6">
                    <h1 class="text-xl font-medium text-gray-800 letter-spacing-wide">SURAT LAMARAN KERJA</h1>
                    <div class="flex justify-center items-center mt-3">
                        <div class="w-2 h-2 bg-yellow-400 rounded-full"></div>
                        <div class="w-8 h-px bg-yellow-300 mx-1"></div>
                        <div class="w-1 h-1 bg-yellow-200 rounded-full"></div>
                    </div>
                </div>

                <div class="flex justify-between items-center mb-6">
                    <div class="text-gray-700">
                        <p class="text-sm">{{subject}}</p>
                    </div>
                    <div class="text-right text-yellow-600">
                        <p class="font-medium">{{date}}</p>
                    </div>
                </div>

                <div class="text-gray-700">
                    {{#recipientLines}}
                    <p>{{.}}</p>
                    {{/recipientLines}}
                </div>
            </div>

            <!-- Content Body -->
            <div class="content-body px-10 pb-40">
                <div class="space-y-5 text-justify text-gray-700">
                    <div class="mb-5">
                        <div class="w-3 h-px bg-yellow-400 mb-3"></div>
                        <p>{{opening}}</p>
                    </div>

                    {{#paragraphs}}
                    <p>{{.}}</p>
                    {{/paragraphs}}

                    {{#closing}}
                    <p>{{.}}</p>
                    {{/closing}}
                </div>
            </div>

            <!-- Signature Section - Fixed at Bottom -->
            <div class="signature-section">
                <p class="font-medium text-gray-800 mb-16">{{farewell}}</p>
                <div class="flex items-end justify-between">
                    <div>
                        <p class="font-medium text-gray-800">{{signatureName}}</p>
                        <div class="w-24 h-px bg-yellow-400 mt-2"></div>
                        {{#additionalInfo}}
                        <p class="text-gray-600 text-sm mt-2">{{.}}</p>
                        {{/additionalInfo}}
                    </div>
                </div>
            </div>

            <!-- Bottom Decoration - Separate from signature -->
            <div class="bottom-decoration">
                <div class="flex items-center">
                    <div class="w-1 h-1 bg-yellow-200 rounded-full"></div>
                    <div class="w-2 h-px bg-yellow-300 mx-1"></div>
                    <div class="w-1 h-1 bg-yellow-400 rounded-full"></div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>"""

private const val MINIMALIST_CIRCULAR_ACCENTS_TEMPLATE = """<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Surat Lamaran Kerja</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;500;600&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Open Sans', sans-serif; }
        .a4-page { height: 297mm; width: 210mm; margin: 0 auto; overflow: hidden; }
        .letter-content { height: 100%; display: flex; flex-direction: column; font-size: 14px; line-height: 1.6; }
        .content-body { flex-grow: 1; }
        @media print { .a4-page { margin: 0; } }
    </style>
</head>
<body class="bg-white m-0 p-0">
    <div class="a4-page bg-white">
        <div class="letter-content">
            <!-- Header with Circles -->
            <div class="px-8 pt-8 pb-6 relative">
                <div class="absolute top-8 left-4 w-3 h-3 bg-purple-300 rounded-full opacity-60"></div>
                <div class="absolute top-12 right-8 w-2 h-2 bg-purple-200 rounded-full opacity-40"></div>
                <div class="absolute top-8 right-16 w-1 h-1 bg-purple-400 rounded-full"></div>

                <div class="text-center mb-6">
                    <h1 class="text-lg font-medium text-gray-800 mb-3">SURAT LAMARAN KERJA</h1>
                    <div class="flex justify-center items-center space-x-2">
                        <div class="w-2 h-2 bg-purple-400 rounded-full"></div>
                        <div class="w-12 h-px bg-purple-300"></div>
                        <div class="w-2 h-2 bg-purple-400 rounded-full"></div>
                    </div>
                </div>
            </div>

            <!-- Date and Subject -->
            <div class="px-8 pb-6">
                <div class="flex items-start justify-between mb-6">
                    <div class="flex items-start space-x-3">
                        <div>
                            <p class="text-gray-700 text-sm">{{subject}}</p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-2">
                        <p class="text-purple-700 font-medium text-sm">{{date}}</p>
                    </div>
                </div>

                <div class="relative">
                    <div class="absolute top-2 right-2 w-2 h-2 bg-purple-200 rounded-full opacity-60"></div>
                    <div class="text-gray-700 text-sm">
                        {{#recipientLines}}
                        <p>{{.}}</p>
                        {{/recipientLines}}
                    </div>
                </div>
            </div>

            <!-- Content Body -->
            <div class="content-body px-8">
                <div class="space-y-4 text-justify text-gray-700">

                    <p>{{opening}}</p>

                    {{#paragraphs}}
                    <p>{{.}}</p>
                    {{/paragraphs}}

                    {{#closing}}
                    <p>{{.}}</p>
                    {{/closing}}
                </div>
            </div>

            <!-- Signature Section - Fixed at bottom -->
            <div class="px-8 pb-8 mt-auto">
                <div class="pt-6">
                    <p class="font-medium text-gray-800 mb-16">{{farewell}}</p>
                    <div>
                        <p class="font-medium text-gray-800 mb-2">{{signatureName}}</p>
                        <div class="flex items-center space-x-1">
                            <div class="w-32 h-px bg-purple-400"></div>
                            <div class="w-1 h-1 bg-purple-400 rounded-full"></div>
                        </div>
                        {{#additionalInfo}}
                        <p class="text-gray-600 text-sm mt-2">{{.}}</p>
                        {{/additionalInfo}}
                    </div>
                </div>

                <!-- Bottom Decorative Elements with spacing -->
                <div class="flex justify-end mt-8 pt-4">
                    <div class="flex space-x-1">
                        <div class="w-1 h-1 bg-purple-200 rounded-full"></div>
                        <div class="w-2 h-2 bg-purple-300 rounded-full opacity-60"></div>
                        <div class="w-1 h-1 bg-purple-400 rounded-full"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>"""
