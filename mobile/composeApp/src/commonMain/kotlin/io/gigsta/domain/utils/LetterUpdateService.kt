package io.gigsta.domain.utils

import io.gigsta.data.network.SupabaseClient
import io.gigsta.domain.repository.AuthRepository
import co.touchlab.kermit.Logger
import kotlinx.serialization.json.Json
import kotlinx.serialization.encodeToString
import io.github.jan.supabase.postgrest.from
import io.github.jan.supabase.postgrest.query.Columns

/**
 * Service for updating letters in Supabase database
 * Mobile equivalent of web's updateLetterWithGeneratedContent function
 */
class LetterUpdateService(
    private val authRepository: AuthRepository
) {
    private val supabase = SupabaseClient.client
    
    /**
     * Update letter with generated content and deduct tokens
     * Mobile equivalent of web's updateLetterWithGeneratedContent function
     */
    suspend fun updateLetterWithGeneratedContent(
        letterId: String,
        plainText: String,
        designHtml: String?,
        templateId: String,
        structuredData: StructuredLetterData?
    ): Result<Unit> {
        return try {
            Logger.d { "Updating letter $letterId with generated content" }
            
            // Update the letter with generated content
            val updateResult = updateLetterContent(letterId, plainText, designHtml, structuredData)
            
            if (updateResult.isFailure) {
                Logger.e { "Failed to update letter content: ${updateResult.exceptionOrNull()?.message}" }
                return updateResult
            }
            
            // Deduct tokens after successful generation
            deductTokensForTemplate(letterId, templateId)
            
            Logger.d { "Successfully updated letter $letterId with generated content" }
            Result.success(Unit)
        } catch (error: Exception) {
            Logger.e(error) { "Error in updateLetterWithGeneratedContent: ${error.message}" }
            Result.failure(error)
        }
    }
    
    /**
     * Update letter content in database
     */
    private suspend fun updateLetterContent(
        letterId: String,
        plainText: String,
        designHtml: String?,
        structuredData: StructuredLetterData?
    ): Result<Unit> {
        return try {
            val updateData = mutableMapOf<String, Any?>(
                "plain_text" to plainText,
                "updated_at" to kotlinx.datetime.Clock.System.now().toString()
            )
            
            if (designHtml != null) {
                updateData["design_html"] = designHtml
            }
            
            if (structuredData != null) {
                updateData["structured_data"] = Json.encodeToString(structuredData)
            }
            
            supabase.from("letters")
                .update(updateData) {
                    filter {
                        eq("id", letterId)
                    }
                }
            
            Logger.d { "Letter content updated successfully" }
            Result.success(Unit)
        } catch (error: Exception) {
            Logger.e(error) { "Error updating letter content: ${error.message}" }
            Result.failure(Exception("Failed to update letter content: ${error.message}"))
        }
    }
    
    /**
     * Deduct tokens for template usage after successful generation
     * Mobile equivalent of web's deductTokensForTemplate function
     */
    private suspend fun deductTokensForTemplate(letterId: String, templateId: String) {
        try {
            Logger.d { "Deducting tokens for template $templateId" }
            
            // Get the user ID from the letter
            val letter = supabase.from("letters")
                .select(columns = Columns.list("user_id")) {
                    filter {
                        eq("id", letterId)
                    }
                }
                .decodeSingleOrNull<LetterUserInfo>()
            
            if (letter == null) {
                Logger.e { "Letter not found for token deduction: $letterId" }
                return
            }
            
            val userId = letter.user_id
            val template = getLetterTemplateById(templateId)
            val tokenCost = template?.tokenCost ?: 0
            
            if (tokenCost <= 0) {
                Logger.d { "No tokens to deduct for template $templateId" }
                return
            }
            
            // Get current token balance
            val profile = supabase.from("profiles")
                .select(columns = Columns.list("tokens")) {
                    filter {
                        eq("id", userId)
                    }
                }
                .decodeSingleOrNull<UserProfile>()
            
            if (profile == null) {
                Logger.e { "User profile not found for token deduction: $userId" }
                return
            }
            
            val currentTokens = profile.tokens ?: 0
            val newTokens = maxOf(currentTokens - tokenCost, 0)
            
            // Update token balance
            supabase.from("profiles")
                .update(mapOf("tokens" to newTokens)) {
                    filter {
                        eq("id", userId)
                    }
                }
            
            Logger.d { "Successfully deducted $tokenCost tokens from user $userId. New balance: $newTokens" }
        } catch (error: Exception) {
            Logger.e(error) { "Unexpected error during token deduction: ${error.message}" }
        }
    }
}

/**
 * Data classes for Supabase queries
 */
@kotlinx.serialization.Serializable
private data class LetterUserInfo(
    val user_id: String
)

@kotlinx.serialization.Serializable
private data class UserProfile(
    val tokens: Int?
)

/**
 * Generate plain text and design HTML from structured data if missing
 * Mobile equivalent of web's transformLetterData logic
 */
suspend fun generateMissingLetterContent(
    letterId: String,
    plainText: String?,
    designHtml: String?,
    structuredData: StructuredLetterData?,
    templateId: String,
    authRepository: AuthRepository
): Pair<String, String?> {
    var finalPlainText = plainText ?: ""
    var finalDesignHtml = designHtml
    
    // If we have structured data but missing plain_text or design_html, generate them
    if (structuredData != null && (finalPlainText.isEmpty() || finalDesignHtml == null)) {
        try {
            Logger.d { "Generating missing content for letter $letterId" }
            
            // Generate plain text if missing
            if (finalPlainText.isEmpty()) {
                finalPlainText = convertStructuredDataToPlainText(structuredData)
                Logger.d { "Generated plain text for letter $letterId" }
            }
            
            // Generate design HTML if missing
            if (finalDesignHtml == null) {
                val template = getLetterTemplateById(templateId)
                if (template != null) {
                    finalDesignHtml = fillLetterTemplate(template, structuredData)
                    Logger.d { "Generated design HTML for letter $letterId using template $templateId" }
                }
            }
            
            // Update the database with generated content if we generated anything new
            if ((plainText.isNullOrEmpty() && finalPlainText.isNotEmpty()) || 
                (designHtml == null && finalDesignHtml != null)) {
                
                val letterUpdateService = LetterUpdateService(authRepository)
                letterUpdateService.updateLetterWithGeneratedContent(
                    letterId = letterId,
                    plainText = finalPlainText,
                    designHtml = finalDesignHtml,
                    templateId = templateId,
                    structuredData = structuredData
                )
                Logger.d { "Updated letter $letterId with generated content" }
            }
        } catch (error: Exception) {
            Logger.e(error) { "Error generating missing content for letter $letterId: ${error.message}" }
            // Continue with whatever we have
        }
    }
    
    return Pair(finalPlainText, finalDesignHtml)
}
