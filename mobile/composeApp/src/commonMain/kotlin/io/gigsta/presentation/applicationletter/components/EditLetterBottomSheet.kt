package io.gigsta.presentation.applicationletter.components

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.Save
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import io.gigsta.domain.utils.StructuredLetterData
import io.gigsta.presentation.theme.Spacing

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun EditLetterBottomSheet(
    structuredData: StructuredLetterData,
    onDataChanged: (StructuredLetterData) -> Unit,
    onSave: () -> Unit,
    onDismiss: () -> Unit,
    isRegenerating: Boolean = false,
    modifier: Modifier = Modifier
) {
    var editedData by remember(structuredData) { mutableStateOf(structuredData) }
    val scrollState = rememberScrollState()

    LaunchedEffect(editedData) {
        onDataChanged(editedData)
    }

    ModalBottomSheet(
        onDismissRequest = onDismiss,
        modifier = modifier,
        dragHandle = {
            Surface(
                modifier = Modifier.padding(vertical = Spacing.small),
                color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.4f),
                shape = MaterialTheme.shapes.extraLarge
            ) {
                Box(
                    modifier = Modifier.size(width = 32.dp, height = 4.dp)
                )
            }
        }
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(Spacing.medium)
        ) {
            // Header
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "Edit Surat Lamaran",
                    style = MaterialTheme.typography.headlineSmall,
                    fontWeight = FontWeight.Bold
                )
                
                Row(
                    horizontalArrangement = Arrangement.spacedBy(Spacing.small)
                ) {
                    IconButton(onClick = onDismiss) {
                        Icon(
                            imageVector = Icons.Default.Close,
                            contentDescription = "Tutup"
                        )
                    }
                    
                    Button(
                        onClick = onSave,
                        enabled = !isRegenerating
                    ) {
                        if (isRegenerating) {
                            CircularProgressIndicator(
                                modifier = Modifier.size(16.dp),
                                strokeWidth = 2.dp
                            )
                        } else {
                            Icon(
                                imageVector = Icons.Default.Save,
                                contentDescription = null,
                                modifier = Modifier.size(16.dp)
                            )
                        }
                        Spacer(modifier = Modifier.width(Spacing.small))
                        Text(if (isRegenerating) "Menyimpan..." else "Simpan")
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(Spacing.medium))
            
            // Scrollable content
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .verticalScroll(scrollState),
                verticalArrangement = Arrangement.spacedBy(Spacing.large)
            ) {
                // Header Section
                EditSection(
                    title = "📅 Header",
                    backgroundColor = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.3f)
                ) {
                    OutlinedTextField(
                        value = editedData.header.date,
                        onValueChange = { newDate ->
                            editedData = editedData.copy(
                                header = editedData.header.copy(date = newDate)
                            )
                        },
                        label = { Text("Tanggal Surat") },
                        placeholder = { Text("27 Mei 2025") },
                        enabled = !isRegenerating,
                        modifier = Modifier.fillMaxWidth()
                    )
                }
                
                // Subject Section
                EditSection(
                    title = "📋 Perihal",
                    backgroundColor = MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.3f)
                ) {
                    OutlinedTextField(
                        value = editedData.subject.prefix,
                        onValueChange = { newPrefix ->
                            editedData = editedData.copy(
                                subject = editedData.subject.copy(prefix = newPrefix)
                            )
                        },
                        label = { Text("Prefix Perihal") },
                        placeholder = { Text("Perihal: Lamaran Pekerjaan sebagai") },
                        enabled = !isRegenerating,
                        modifier = Modifier.fillMaxWidth()
                    )
                    
                    Spacer(modifier = Modifier.height(Spacing.medium))
                    
                    OutlinedTextField(
                        value = editedData.subject.position,
                        onValueChange = { newPosition ->
                            editedData = editedData.copy(
                                subject = editedData.subject.copy(position = newPosition)
                            )
                        },
                        label = { Text("Posisi yang Dilamar") },
                        placeholder = { Text("Fullstack Developer") },
                        enabled = !isRegenerating,
                        modifier = Modifier.fillMaxWidth()
                    )
                }
                
                // Recipient Section
                EditSection(
                    title = "🏢 Penerima Surat",
                    backgroundColor = MaterialTheme.colorScheme.secondaryContainer.copy(alpha = 0.3f)
                ) {
                    OutlinedTextField(
                        value = editedData.recipient.salutation,
                        onValueChange = { newSalutation ->
                            editedData = editedData.copy(
                                recipient = editedData.recipient.copy(salutation = newSalutation)
                            )
                        },
                        label = { Text("Sapaan") },
                        placeholder = { Text("Yth.") },
                        enabled = !isRegenerating,
                        modifier = Modifier.fillMaxWidth()
                    )
                    
                    Spacer(modifier = Modifier.height(Spacing.medium))
                    
                    OutlinedTextField(
                        value = editedData.recipient.title,
                        onValueChange = { newTitle ->
                            editedData = editedData.copy(
                                recipient = editedData.recipient.copy(title = newTitle)
                            )
                        },
                        label = { Text("Penerima") },
                        placeholder = { Text("Bapak/Ibu Bagian Sumber Daya Manusia") },
                        enabled = !isRegenerating,
                        modifier = Modifier.fillMaxWidth()
                    )
                    
                    Spacer(modifier = Modifier.height(Spacing.medium))
                    
                    OutlinedTextField(
                        value = editedData.recipient.company ?: "",
                        onValueChange = { newCompany ->
                            editedData = editedData.copy(
                                recipient = editedData.recipient.copy(company = newCompany.takeIf { it.isNotBlank() })
                            )
                        },
                        label = { Text("Nama Perusahaan") },
                        placeholder = { Text("PT Example Company") },
                        enabled = !isRegenerating,
                        modifier = Modifier.fillMaxWidth()
                    )
                }

                // Body Section
                EditSection(
                    title = "📄 Isi Surat",
                    backgroundColor = MaterialTheme.colorScheme.tertiaryContainer.copy(alpha = 0.3f)
                ) {
                    OutlinedTextField(
                        value = editedData.body.opening,
                        onValueChange = { newOpening ->
                            editedData = editedData.copy(
                                body = editedData.body.copy(opening = newOpening)
                            )
                        },
                        label = { Text("Pembuka Surat") },
                        placeholder = { Text("Dengan hormat,") },
                        enabled = !isRegenerating,
                        modifier = Modifier.fillMaxWidth()
                    )

                    Spacer(modifier = Modifier.height(Spacing.medium))

                    // Paragraphs
                    Text(
                        text = "Isi Surat (Paragraf)",
                        style = MaterialTheme.typography.labelMedium,
                        modifier = Modifier.padding(bottom = Spacing.small)
                    )

                    editedData.body.paragraphs.forEachIndexed { index, paragraph ->
                        OutlinedTextField(
                            value = paragraph,
                            onValueChange = { newParagraph ->
                                val newParagraphs = editedData.body.paragraphs.toMutableList()
                                newParagraphs[index] = newParagraph
                                editedData = editedData.copy(
                                    body = editedData.body.copy(paragraphs = newParagraphs)
                                )
                            },
                            label = { Text("Paragraf ${index + 1}") },
                            placeholder = { Text("Isi paragraf ${index + 1}...") },
                            enabled = !isRegenerating,
                            modifier = Modifier.fillMaxWidth(),
                            minLines = 3,
                            maxLines = 5
                        )

                        if (index < editedData.body.paragraphs.size - 1) {
                            Spacer(modifier = Modifier.height(Spacing.medium))
                        }
                    }

                    Spacer(modifier = Modifier.height(Spacing.medium))

                    OutlinedTextField(
                        value = editedData.body.closing,
                        onValueChange = { newClosing ->
                            editedData = editedData.copy(
                                body = editedData.body.copy(closing = newClosing)
                            )
                        },
                        label = { Text("Penutup Surat") },
                        placeholder = { Text("Atas perhatian dan waktu yang Bapak/Ibu berikan, saya ucapkan terima kasih.") },
                        enabled = !isRegenerating,
                        modifier = Modifier.fillMaxWidth(),
                        minLines = 2,
                        maxLines = 3
                    )
                }

                // Signature Section
                EditSection(
                    title = "✍️ Tanda Tangan",
                    backgroundColor = MaterialTheme.colorScheme.errorContainer.copy(alpha = 0.3f)
                ) {
                    OutlinedTextField(
                        value = editedData.signature.farewell,
                        onValueChange = { newFarewell ->
                            editedData = editedData.copy(
                                signature = editedData.signature.copy(farewell = newFarewell)
                            )
                        },
                        label = { Text("Salam Penutup") },
                        placeholder = { Text("Hormat saya,") },
                        enabled = !isRegenerating,
                        modifier = Modifier.fillMaxWidth()
                    )

                    Spacer(modifier = Modifier.height(Spacing.medium))

                    OutlinedTextField(
                        value = editedData.signature.name,
                        onValueChange = { newName ->
                            editedData = editedData.copy(
                                signature = editedData.signature.copy(name = newName)
                            )
                        },
                        label = { Text("Nama Pengirim") },
                        placeholder = { Text("John Doe") },
                        enabled = !isRegenerating,
                        modifier = Modifier.fillMaxWidth()
                    )
                }

                // Add bottom padding for navigation bar
                Spacer(modifier = Modifier.height(Spacing.extraLarge))
            }
        }
    }
}

@Composable
private fun EditSection(
    title: String,
    backgroundColor: androidx.compose.ui.graphics.Color,
    modifier: Modifier = Modifier,
    content: @Composable ColumnScope.() -> Unit
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(containerColor = backgroundColor),
        shape = MaterialTheme.shapes.medium
    ) {
        Column(
            modifier = Modifier.padding(Spacing.medium)
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.SemiBold,
                modifier = Modifier.padding(bottom = Spacing.medium)
            )
            
            content()
        }
    }
}
