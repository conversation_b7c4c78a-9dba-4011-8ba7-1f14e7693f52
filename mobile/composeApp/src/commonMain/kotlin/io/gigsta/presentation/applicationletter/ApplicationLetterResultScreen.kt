package io.gigsta.presentation.applicationletter

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.animateFloat
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.rememberInfiniteTransition
import androidx.compose.animation.core.tween
import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.RepeatMode
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.draw.scale
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.drawscope.DrawScope
import kotlin.math.cos
import kotlin.math.sin
import kotlin.math.PI
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInVertically
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBars
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.windowInsetsPadding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.AutoAwesome
import androidx.compose.material.icons.filled.Check
import androidx.compose.material.icons.filled.CheckCircle
import androidx.compose.material.icons.filled.ContentCopy
import androidx.compose.material.icons.filled.ErrorOutline
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material.icons.filled.Download
import androidx.compose.material.icons.filled.Edit
import androidx.compose.material.icons.filled.Share
import androidx.compose.material.icons.filled.Warning
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.CenterAlignedTopAppBar
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.LinearProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.OutlinedCard
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.material3.surfaceColorAtElevation
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalClipboardManager
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import co.touchlab.kermit.Logger
import io.gigsta.presentation.theme.Spacing
import io.gigsta.presentation.components.LetterPreview
import io.gigsta.presentation.applicationletter.components.EditLetterBottomSheet
import io.gigsta.utils.rememberFileSharer
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import androidx.compose.runtime.rememberCoroutineScope

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ApplicationLetterResultScreen(
    onNavigateBack: () -> Unit,
    viewModel: ApplicationLetterViewModel = viewModel(),
    modifier: Modifier = Modifier
) {
    val uiState = viewModel.uiState
    val fileSharer = rememberFileSharer()
    val coroutineScope = rememberCoroutineScope()

    // Handle PDF download
    val handlePdfDownload = {
        coroutineScope.launch {
            val result = viewModel.downloadLetterPDF()
            result?.fold(
                onSuccess = { pdfData ->
                    val fileName = "Surat_Lamaran_Gigsta_${uiState.applicationLetter?.templateName?.replace(" ", "_") ?: "Letter"}"
                    // Try to share the PDF
                    fileSharer.sharePDF(pdfData, fileName).fold(
                        onSuccess = {
                            // PDF shared successfully
                        },
                        onFailure = { error ->
                            // Handle sharing error
                            Logger.e { "Failed to share PDF: ${error.message}" }
                        }
                    )
                },
                onFailure = { error ->
                    Logger.e { "Failed to download PDF: ${error.message}" }
                }
            )
        }
    }

    Scaffold(
        topBar = {
            CenterAlignedTopAppBar(
                title = {
                    Text(
                        text = "Hasil Surat Lamaran",
                        fontWeight = FontWeight.Bold,
                        style = MaterialTheme.typography.titleLarge
                    )
                },
                navigationIcon = {
                    IconButton(onClick = {
                        if (uiState.isGenerating) {
                            viewModel.cancelGeneration()
                        }
                        onNavigateBack()
                    }) {
                        Icon(
                            imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                            contentDescription = "Kembali"
                        )
                    }
                },
                colors = TopAppBarDefaults.centerAlignedTopAppBarColors(
                    containerColor = MaterialTheme.colorScheme.surfaceColorAtElevation(2.dp)
                )
            )
        },
        bottomBar = {
            AnimatedVisibility(
                visible = uiState.applicationLetter != null && !uiState.isGenerating
            ) {
                Surface(
                    modifier = Modifier.fillMaxWidth(),
                    shadowElevation = 8.dp
                ) {
                    Column {
                        // First row: Edit and Regenerate
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(horizontal = Spacing.medium)
                                .padding(top = Spacing.medium),
                            horizontalArrangement = Arrangement.spacedBy(Spacing.medium)
                        ) {
                            OutlinedButton(
                                onClick = viewModel::startEditing,
                                enabled = !uiState.isGenerating && !uiState.isRegenerating,
                                modifier = Modifier.weight(1f),
                                contentPadding = PaddingValues(Spacing.medium)
                            ) {
                                Icon(Icons.Default.Edit, contentDescription = "Edit")
                                Spacer(Modifier.width(Spacing.small))
                                Text("Edit")
                            }

                            OutlinedButton(
                                onClick = viewModel::regenerateLetter,
                                enabled = !uiState.isGenerating && !uiState.isRegenerating,
                                modifier = Modifier.weight(1f),
                                contentPadding = PaddingValues(Spacing.medium)
                            ) {
                                if (uiState.isGenerating || uiState.isRegenerating) {
                                    CircularProgressIndicator(Modifier.size(20.dp))
                                } else {
                                    Icon(Icons.Default.Refresh, contentDescription = "Buat Ulang")
                                    Spacer(Modifier.width(Spacing.small))
                                    Text("Buat Ulang")
                                }
                            }
                        }

                        // Second row: Download PDF
                        Button(
                            onClick = handlePdfDownload,
                            enabled = !uiState.isDownloadingPdf && !uiState.isGenerating && !uiState.isRegenerating,
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(horizontal = Spacing.medium)
                                .padding(bottom = Spacing.medium),
                            contentPadding = PaddingValues(Spacing.medium)
                        ) {
                            if (uiState.isDownloadingPdf) {
                                CircularProgressIndicator(Modifier.size(20.dp))
                                Spacer(Modifier.width(Spacing.small))
                                Text("Mengunduh...")
                            } else {
                                Icon(Icons.Default.Download, contentDescription = null)
                                Spacer(Modifier.width(Spacing.small))
                                Text("Unduh PDF")
                            }
                        }
                    }
                }
            }
        }
    ) { paddingValues ->
        Box(
            modifier = modifier
                .fillMaxSize()
                .padding(paddingValues),
            contentAlignment = Alignment.Center
        ) {
            AnimatedVisibility(
                visible = uiState.isGenerating,
                enter = fadeIn() + slideInVertically(),
                exit = fadeOut()
            ) {
                GeneratingLetterContent(
                    modifier = Modifier.fillMaxSize()
                )
            }

            AnimatedVisibility(
                visible = uiState.applicationLetter != null && !uiState.isGenerating,
                enter = fadeIn() + slideInVertically(),
                exit = fadeOut()
            ) {
                LetterResultSuccessContent(
                    applicationLetter = uiState.applicationLetter!!,
                    modifier = Modifier.fillMaxSize()
                )
            }

            AnimatedVisibility(
                visible = uiState.error != null,
                enter = fadeIn() + slideInVertically(),
                exit = fadeOut()
            ) {
                ErrorContent(
                    error = uiState.error ?: "",
                    onRetry = viewModel::regenerateLetter,
                    onGoBack = onNavigateBack,
                    modifier = Modifier.fillMaxSize()
                )
            }

            if (uiState.applicationLetter == null && uiState.error == null && !uiState.isGenerating) {
                LaunchedEffect(Unit) {
                    onNavigateBack()
                }
            }
        }

        // Edit Bottom Sheet
        if (uiState.isEditing && uiState.editedStructuredData != null) {
            EditLetterBottomSheet(
                structuredData = uiState.editedStructuredData,
                onDataChanged = viewModel::updateEditedStructuredData,
                onSave = {
                    viewModel.regenerateFromEditedData()
                },
                onDismiss = viewModel::cancelEditing,
                isRegenerating = uiState.isRegenerating
            )
        }
    }
}

@Composable
private fun LetterResultSuccessContent(
    applicationLetter: ApplicationLetter,
    modifier: Modifier = Modifier
) {
    val scrollState = rememberScrollState()
    val clipboardManager = LocalClipboardManager.current
    var contentCopied by remember { mutableStateOf(false) }
    var showPlainText by remember { mutableStateOf(false) }

    LaunchedEffect(contentCopied) {
        if (contentCopied) {
            delay(2000)
            contentCopied = false
        }
    }

    Column(
        modifier = modifier
            .verticalScroll(scrollState)
            .padding(Spacing.large),
        verticalArrangement = Arrangement.spacedBy(Spacing.extraLarge)
    ) {
        // Success Header
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier.fillMaxWidth()
        ) {
            Icon(
                imageVector = Icons.Default.CheckCircle,
                contentDescription = null,
                modifier = Modifier.size(48.dp),
                tint = MaterialTheme.colorScheme.primary
            )

            Spacer(modifier = Modifier.height(Spacing.medium))

            Text(
                text = "Surat Lamaran Anda Telah Siap!",
                style = MaterialTheme.typography.headlineMedium,
                fontWeight = FontWeight.Bold,
                textAlign = TextAlign.Center
            )

            Text(
                text = "Surat lamaran profesional telah digenerate sesuai dengan resume dan posisi yang Anda pilih menggunakan template ${applicationLetter.templateName}",
                style = MaterialTheme.typography.bodyMedium,
                textAlign = TextAlign.Center,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                modifier = Modifier.padding(horizontal = Spacing.medium)
            )
        }

        // Toggle buttons for preview/text view
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(Spacing.small)
        ) {
            OutlinedButton(
                onClick = { showPlainText = false },
                modifier = Modifier.weight(1f),
                colors = ButtonDefaults.outlinedButtonColors(
                    containerColor = if (!showPlainText) MaterialTheme.colorScheme.primary
                                   else MaterialTheme.colorScheme.surface,
                    contentColor = if (!showPlainText) MaterialTheme.colorScheme.onPrimary
                                 else MaterialTheme.colorScheme.primary
                )
            ) {
                Text("Pratinjau")
            }

            OutlinedButton(
                onClick = { showPlainText = true },
                modifier = Modifier.weight(1f),
                colors = ButtonDefaults.outlinedButtonColors(
                    containerColor = if (showPlainText) MaterialTheme.colorScheme.primary
                                   else MaterialTheme.colorScheme.surface,
                    contentColor = if (showPlainText) MaterialTheme.colorScheme.onPrimary
                                 else MaterialTheme.colorScheme.primary
                )
            ) {
                Text("Teks")
            }
        }

        // Content based on toggle
        if (showPlainText) {
            // Plain text view
            CopyableContentCard(
                title = "Surat Lamaran",
                content = applicationLetter.plainText,
                isCopied = contentCopied,
                onCopy = {
                    clipboardManager.setText(AnnotatedString(applicationLetter.plainText))
                    contentCopied = true
                }
            )
        } else {
            // Letter preview
            Logger.d { "Design HTML: ${applicationLetter.designHtml}" }
            if (applicationLetter.designHtml != null) {
                LetterPreview(
                    htmlContent = applicationLetter.designHtml,
                    templateName = applicationLetter.templateName,
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(600.dp)
                )
            } else {
                // Fallback to plain text if no design HTML
                CopyableContentCard(
                    title = "Surat Lamaran",
                    content = applicationLetter.plainText,
                    isCopied = contentCopied,
                    onCopy = {
                        clipboardManager.setText(AnnotatedString(applicationLetter.plainText))
                        contentCopied = true
                    }
                )
            }
        }
    }
}

@Composable
private fun GeneratingLetterContent(
    modifier: Modifier = Modifier
) {
    val infiniteTransition = rememberInfiniteTransition(label = "generating_animation")

    // Multiple animations for different effects
    val pulseScale by infiniteTransition.animateFloat(
        initialValue = 0.8f,
        targetValue = 1.2f,
        animationSpec = infiniteRepeatable(
            animation = tween(2000),
            repeatMode = RepeatMode.Reverse
        ),
        label = "pulse_scale"
    )

    val rotation by infiniteTransition.animateFloat(
        initialValue = 0f,
        targetValue = 360f,
        animationSpec = infiniteRepeatable(
            animation = tween(6000, easing = LinearEasing),
            repeatMode = RepeatMode.Restart
        ),
        label = "rotation"
    )

    val orbit1 by infiniteTransition.animateFloat(
        initialValue = 0f,
        targetValue = 360f,
        animationSpec = infiniteRepeatable(
            animation = tween(3000, easing = LinearEasing)
        ),
        label = "orbit1"
    )

    val orbit2 by infiniteTransition.animateFloat(
        initialValue = 0f,
        targetValue = 360f,
        animationSpec = infiniteRepeatable(
            animation = tween(4500, easing = LinearEasing)
        ),
        label = "orbit2"
    )

    val orbit3 by infiniteTransition.animateFloat(
        initialValue = 0f,
        targetValue = 360f,
        animationSpec = infiniteRepeatable(
            animation = tween(5500, easing = LinearEasing)
        ),
        label = "orbit3"
    )

    val shimmer by infiniteTransition.animateFloat(
        initialValue = 0f,
        targetValue = 1f,
        animationSpec = infiniteRepeatable(
            animation = tween(1500),
            repeatMode = RepeatMode.Reverse
        ),
        label = "shimmer"
    )

    Column(
        modifier = modifier.padding(Spacing.large),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        // Animated icon container with floating particles
        Box(
            modifier = Modifier.size(120.dp),
            contentAlignment = Alignment.Center
        ) {
            // Background glow effect
            Box(
                modifier = Modifier
                    .size(80.dp)
                    .scale(pulseScale)
                    .alpha(0.3f)
                    .background(
                        brush = Brush.radialGradient(
                            colors = listOf(
                                MaterialTheme.colorScheme.primary.copy(alpha = shimmer * 0.6f),
                                MaterialTheme.colorScheme.primary.copy(alpha = 0f)
                            )
                        ),
                        shape = CircleShape
                    )
            )

            // Floating particles canvas
            val primaryColor = MaterialTheme.colorScheme.primary
            Canvas(modifier = Modifier.fillMaxSize()) {
                drawFloatingParticles(
                    orbit1 = orbit1,
                    orbit2 = orbit2,
                    orbit3 = orbit3,
                    color = primaryColor
                )
            }

            // Main central icon
            Icon(
                imageVector = Icons.Default.AutoAwesome,
                contentDescription = null,
                modifier = Modifier
                    .size(48.dp)
                    .scale(pulseScale * 0.8f + 0.2f)
                    .rotate(rotation),
                tint = MaterialTheme.colorScheme.primary
            )
        }

        Spacer(modifier = Modifier.height(Spacing.extraLarge))

        // Animated text with shimmer effect
        Text(
            text = "Sedang Meracik Surat Lamaran Terbaik...",
            style = MaterialTheme.typography.headlineSmall,
            fontWeight = FontWeight.SemiBold,
            textAlign = TextAlign.Center,
            modifier = Modifier.alpha(0.7f + shimmer * 0.3f)
        )

        Spacer(modifier = Modifier.height(Spacing.medium))

        Text(
            text = "AI kami sedang menganalisis CV dan lowongan untuk membuat surat lamaran yang paling menonjol",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            textAlign = TextAlign.Center,
            modifier = Modifier.alpha(0.8f + shimmer * 0.2f)
        )

        Spacer(modifier = Modifier.height(Spacing.large))

        // Enhanced progress indicator
        LinearProgressIndicator(
            modifier = Modifier
                .fillMaxWidth()
                .alpha(0.8f + shimmer * 0.2f),
            color = MaterialTheme.colorScheme.primary,
            trackColor = MaterialTheme.colorScheme.primary.copy(alpha = 0.2f)
        )
    }
}

private fun DrawScope.drawFloatingParticles(
    orbit1: Float,
    orbit2: Float,
    orbit3: Float,
    color: androidx.compose.ui.graphics.Color
) {
    val centerX = size.width / 2
    val centerY = size.height / 2
    val radius1 = 35.dp.toPx()
    val radius2 = 45.dp.toPx()
    val radius3 = 55.dp.toPx()

    // Convert degrees to radians and calculate positions
    val radian1 = (orbit1 * PI / 180).toFloat()
    val radian2 = (orbit2 * PI / 180).toFloat()
    val radian3 = (orbit3 * PI / 180).toFloat()

    // Particle 1 - small dot
    val x1 = centerX + (radius1 * cos(radian1))
    val y1 = centerY + (radius1 * sin(radian1))
    drawCircle(
        color = color.copy(alpha = 0.8f),
        radius = 4.dp.toPx(),
        center = Offset(x1, y1)
    )

    // Particle 2 - medium dot
    val x2 = centerX + (radius2 * cos(radian2 + PI.toFloat()))
    val y2 = centerY + (radius2 * sin(radian2 + PI.toFloat()))
    drawCircle(
        color = color.copy(alpha = 0.6f),
        radius = 3.dp.toPx(),
        center = Offset(x2, y2)
    )

    // Particle 3 - small dot
    val x3 = centerX + (radius3 * cos(radian3 + (PI/2).toFloat()))
    val y3 = centerY + (radius3 * sin(radian3 + (PI/2).toFloat()))
    drawCircle(
        color = color.copy(alpha = 0.5f),
        radius = 2.5.dp.toPx(),
        center = Offset(x3, y3)
    )

    // Additional particles for more richness
    val x4 = centerX + (radius1 * cos(radian1 + (PI/3).toFloat()))
    val y4 = centerY + (radius1 * sin(radian1 + (PI/3).toFloat()))
    drawCircle(
        color = color.copy(alpha = 0.4f),
        radius = 2.dp.toPx(),
        center = Offset(x4, y4)
    )

    val x5 = centerX + (radius2 * cos(radian2 - (PI/3).toFloat()))
    val y5 = centerY + (radius2 * sin(radian2 - (PI/3).toFloat()))
    drawCircle(
        color = color.copy(alpha = 0.3f),
        radius = 1.5.dp.toPx(),
        center = Offset(x5, y5)
    )
}

@Composable
private fun ErrorContent(
    error: String,
    onRetry: () -> Unit,
    onGoBack: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.padding(Spacing.large),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Icon(
            imageVector = Icons.Default.ErrorOutline,
            contentDescription = null,
            modifier = Modifier.size(80.dp),
            tint = MaterialTheme.colorScheme.error
        )

        Spacer(modifier = Modifier.height(Spacing.large))

        Text(
            text = "Gagal Membuat Surat Lamaran",
            style = MaterialTheme.typography.headlineSmall,
            fontWeight = FontWeight.Bold,
            textAlign = TextAlign.Center
        )

        Spacer(modifier = Modifier.height(Spacing.medium))

        Text(
            text = "Terjadi kesalahan saat kami mencoba membuat surat lamaran untuk Anda. Silakan coba lagi.",
            style = MaterialTheme.typography.bodyMedium,
            textAlign = TextAlign.Center,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )

        Spacer(modifier = Modifier.height(Spacing.medium))

        Card(
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.errorContainer
            ),
            modifier = Modifier.fillMaxWidth()
        ) {
            Row(
                modifier = Modifier.padding(Spacing.medium),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.Warning,
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.onErrorContainer
                )
                Spacer(modifier = Modifier.width(Spacing.small))
                Text(
                    text = "Detail: $error",
                    color = MaterialTheme.colorScheme.onErrorContainer,
                    style = MaterialTheme.typography.bodyMedium
                )
            }
        }

        Spacer(modifier = Modifier.height(Spacing.extraLarge))

        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(Spacing.medium)
        ) {
            OutlinedButton(
                onClick = onGoBack,
                modifier = Modifier.weight(1f)
            ) {
                Text("Kembali")
            }

            Button(
                onClick = onRetry,
                modifier = Modifier.weight(1f)
            ) {
                Icon(Icons.Default.Refresh, contentDescription = null)
                Spacer(Modifier.width(Spacing.small))
                Text("Coba Lagi")
            }
        }
    }
}

@Composable
private fun CopyableContentCard(
    title: String,
    content: String,
    isCopied: Boolean,
    onCopy: () -> Unit,
    modifier: Modifier = Modifier
) {
    OutlinedCard(
        modifier = modifier.fillMaxWidth(),
        shape = MaterialTheme.shapes.large
    ) {
        Column(modifier = Modifier.padding(Spacing.medium)) {
            // Header with title and copy button
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = title,
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.SemiBold
                )

                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(Spacing.small)
                ) {
                    AnimatedVisibility(
                        visible = isCopied,
                        enter = fadeIn(),
                        exit = fadeOut()
                    ) {
                        Text(
                            text = "Tersalin!",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.primary,
                            fontWeight = FontWeight.Medium
                        )
                    }

                    IconButton(
                        onClick = onCopy,
                        modifier = Modifier.size(40.dp)
                    ) {
                        Icon(
                            imageVector = if (isCopied) Icons.Default.Check else Icons.Default.ContentCopy,
                            contentDescription = if (isCopied) "Tersalin" else "Salin",
                            tint = if (isCopied) {
                                MaterialTheme.colorScheme.primary
                            } else {
                                MaterialTheme.colorScheme.onSurfaceVariant
                            },
                            modifier = Modifier.size(20.dp)
                        )
                    }
                }
            }

            Spacer(modifier = Modifier.height(Spacing.small))

            // Content
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.3f)
                ),
                shape = MaterialTheme.shapes.medium
            ) {
                Text(
                    text = content,
                    style = MaterialTheme.typography.bodyLarge,
                    lineHeight = 24.sp,
                    modifier = Modifier.padding(Spacing.medium)
                )
            }
        }
    }
}