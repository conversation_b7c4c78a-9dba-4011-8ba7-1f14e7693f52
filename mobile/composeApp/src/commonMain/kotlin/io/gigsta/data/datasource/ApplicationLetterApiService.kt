package io.gigsta.data.datasource

import io.gigsta.data.model.ApplicationLetterResponse
import io.gigsta.data.network.NetworkClient
import io.gigsta.data.network.NetworkConfig
import io.gigsta.data.network.SupabaseClient
import io.gigsta.domain.model.ResumeInfo
import io.gigsta.domain.model.JobInfo
import io.ktor.client.call.*
import io.ktor.client.request.forms.*
import io.ktor.client.request.*
import io.ktor.client.statement.*
import io.ktor.http.*
import io.ktor.client.request.bearerAuth
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.jsonObject
import kotlinx.serialization.json.jsonPrimitive

class ApplicationLetterApiService {

    private val httpClient = NetworkClient.client
    private val supabase = SupabaseClient.client

    suspend fun generateApplicationLetter(
        resumeInfo: ResumeInfo,
        jobInfo: JobInfo,
        templateId: String,
        accessToken: String?
    ): Result<ApplicationLetterResponse> {
        return try {
            val formDataList = formData {
                // Add template ID
                append("\"templateId\"", templateId)

                // Add job description if provided (plain string)
                jobInfo.description?.let { description ->
                    append("\"jobDescription\"", "\"${description}\"")
                }

                // Add job image if provided
                if (jobInfo.imageData != null && jobInfo.imageMimeType != null && jobInfo.imageFileName != null) {
                    append("\"jobImage\"", jobInfo.imageData, Headers.build {
                        append(HttpHeaders.ContentType, jobInfo.imageMimeType)
                        append(
                            HttpHeaders.ContentDisposition,
                            "filename=\"${jobInfo.imageFileName}\""
                        )
                    })
                }
            }

            val response = httpClient.submitFormWithBinaryData(
                url = "${NetworkConfig.API_BASE_URL}/api/generate-application-letter",
                formData = formDataList
            ) {
                // Add Authorization header for server-side auth (no cookies on mobile)
                if (!accessToken.isNullOrBlank()) {
                    bearerAuth(accessToken)
                }
            }

            if (response.status.isSuccess()) {
                val letterResponse = response.body<ApplicationLetterResponse>()
                Result.success(letterResponse)
            } else {
                val errorBody = response.body<String>()
                Result.failure(Exception("API Error: ${response.status.value} - $errorBody"))
            }

        } catch (e: Exception) {
            println("Error generating application letter: ${e.message}")
            Result.failure(Exception("Failed to generate application letter: ${e.message}"))
        }
    }

    /**
     * Download letter as PDF using Supabase Edge Function
     */
    suspend fun downloadLetterPDF(letterId: String, accessToken: String?): Result<ByteArray> {
        return try {
            if (accessToken.isNullOrBlank()) {
                return Result.failure(Exception("Authentication required"))
            }

            // Use Supabase Functions to download PDF
            val response = supabase.functions.invoke(
                function = "download-letter",
                body = mapOf("letterId" to letterId),
                headers = mapOf(
                    "Authorization" to "Bearer $accessToken",
                    "Content-Type" to "application/json"
                )
            )

            if (response.status.isSuccess()) {
                val pdfData = response.body<ByteArray>()
                Result.success(pdfData)
            } else {
                // Try to parse error message from JSON response
                val errorMessage = try {
                    val errorBody = response.body<String>()
                    val json = Json.parseToJsonElement(errorBody)
                    json.jsonObject["error"]?.jsonPrimitive?.content ?: "Failed to download PDF"
                } catch (e: Exception) {
                    "Failed to download PDF: ${response.status.value}"
                }
                Result.failure(Exception(errorMessage))
            }

        } catch (e: Exception) {
            println("Error downloading letter PDF: ${e.message}")
            Result.failure(Exception("Failed to download PDF: ${e.message}"))
        }
    }

    private fun getMimeTypeFromFileName(fileName: String?): String {
        return when (fileName?.lowercase()?.substringAfterLast('.')) {
            "pdf" -> "application/pdf"
            "docx" -> "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
            "doc" -> "application/msword"
            "txt" -> "text/plain"
            "png" -> "image/png"
            "jpg", "jpeg" -> "image/jpeg"
            else -> "application/octet-stream"
        }
    }
}