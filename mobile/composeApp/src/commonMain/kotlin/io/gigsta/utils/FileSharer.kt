package io.gigsta.utils

import androidx.compose.runtime.Composable

/**
 * Platform-specific file sharing utility
 */
expect class FileSharer {
    /**
     * Share a PDF file with other apps
     * @param pdfData The PDF file data as ByteArray
     * @param fileName The name for the file (without extension)
     */
    suspend fun sharePDF(pdfData: ByteArray, fileName: String): Result<Unit>
    
    /**
     * Save a PDF file to device storage
     * @param pdfData The PDF file data as ByteArray
     * @param fileName The name for the file (without extension)
     */
    suspend fun savePDF(pdfData: ByteArray, fileName: String): Result<Unit>
}

/**
 * Composable function to remember a FileSharer instance
 */
@Composable
expect fun rememberFileSharer(): FileSharer
