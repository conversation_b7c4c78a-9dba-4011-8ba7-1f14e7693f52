package io.gigsta.utils

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Environment
import androidx.compose.runtime.Composable
import androidx.compose.ui.platform.LocalContext
import androidx.core.content.FileProvider
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileOutputStream

actual class FileSharer(private val context: Context) {
    
    actual suspend fun sharePDF(pdfData: ByteArray, fileName: String): Result<Unit> {
        return withContext(Dispatchers.IO) {
            try {
                // Create a temporary file in the app's cache directory
                val cacheDir = File(context.cacheDir, "shared_pdfs")
                if (!cacheDir.exists()) {
                    cacheDir.mkdirs()
                }
                
                val file = File(cacheDir, "$fileName.pdf")
                FileOutputStream(file).use { fos ->
                    fos.write(pdfData)
                }
                
                // Get URI using FileProvider
                val uri = FileProvider.getUriForFile(
                    context,
                    "${context.packageName}.fileprovider",
                    file
                )
                
                // Create share intent
                val shareIntent = Intent().apply {
                    action = Intent.ACTION_SEND
                    type = "application/pdf"
                    putExtra(Intent.EXTRA_STREAM, uri)
                    putExtra(Intent.EXTRA_SUBJECT, fileName)
                    addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
                    addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                }
                
                val chooserIntent = Intent.createChooser(shareIntent, "Bagikan PDF")
                chooserIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                context.startActivity(chooserIntent)
                
                Result.success(Unit)
            } catch (e: Exception) {
                Result.failure(Exception("Gagal membagikan PDF: ${e.message}"))
            }
        }
    }
    
    actual suspend fun savePDF(pdfData: ByteArray, fileName: String): Result<Unit> {
        return withContext(Dispatchers.IO) {
            try {
                // Save to Downloads directory
                val downloadsDir = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS)
                if (!downloadsDir.exists()) {
                    downloadsDir.mkdirs()
                }
                
                val file = File(downloadsDir, "$fileName.pdf")
                FileOutputStream(file).use { fos ->
                    fos.write(pdfData)
                }
                
                // Notify media scanner about the new file
                val intent = Intent(Intent.ACTION_MEDIA_SCANNER_SCAN_FILE)
                intent.data = Uri.fromFile(file)
                context.sendBroadcast(intent)
                
                Result.success(Unit)
            } catch (e: Exception) {
                Result.failure(Exception("Gagal menyimpan PDF: ${e.message}"))
            }
        }
    }
}

@Composable
actual fun rememberFileSharer(): FileSharer {
    val context = LocalContext.current
    return FileSharer(context)
}
