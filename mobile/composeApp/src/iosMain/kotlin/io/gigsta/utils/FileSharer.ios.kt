package io.gigsta.utils

import androidx.compose.runtime.Composable
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import platform.Foundation.*
import platform.UIKit.*
import platform.UniformTypeIdentifiers.UTTypePDF

actual class FileSharer {
    
    actual suspend fun sharePDF(pdfData: ByteArray, fileName: String): Result<Unit> {
        return withContext(Dispatchers.Main) {
            try {
                // Convert ByteArray to NSData
                val nsData = pdfData.toNSData()
                
                // Create temporary file URL
                val tempDir = NSTemporaryDirectory()
                val fileURL = NSURL.fileURLWithPath("$tempDir$fileName.pdf")
                
                // Write data to file
                nsData.writeToURL(fileURL, atomically = true)
                
                // Create activity view controller
                val activityViewController = UIActivityViewController(
                    activityItems = listOf(fileURL),
                    applicationActivities = null
                )
                
                // Present the share sheet
                val rootViewController = UIApplication.sharedApplication.keyWindow?.rootViewController
                rootViewController?.presentViewController(
                    activityViewController,
                    animated = true,
                    completion = null
                )
                
                Result.success(Unit)
            } catch (e: Exception) {
                Result.failure(Exception("Gagal membagikan PDF: ${e.message}"))
            }
        }
    }
    
    actual suspend fun savePDF(pdfData: ByteArray, fileName: String): Result<Unit> {
        return withContext(Dispatchers.Main) {
            try {
                // Convert ByteArray to NSData
                val nsData = pdfData.toNSData()
                
                // Get Documents directory
                val documentsPath = NSSearchPathForDirectoriesInDomains(
                    NSDocumentDirectory,
                    NSUserDomainMask,
                    true
                ).first() as String
                
                val fileURL = NSURL.fileURLWithPath("$documentsPath/$fileName.pdf")
                
                // Write data to file
                val success = nsData.writeToURL(fileURL, atomically = true)
                
                if (success) {
                    Result.success(Unit)
                } else {
                    Result.failure(Exception("Gagal menyimpan PDF ke Documents"))
                }
            } catch (e: Exception) {
                Result.failure(Exception("Gagal menyimpan PDF: ${e.message}"))
            }
        }
    }
    
    private fun ByteArray.toNSData(): NSData {
        return NSData.create(
            bytes = this.refTo(0),
            length = this.size.toULong()
        )
    }
}

@Composable
actual fun rememberFileSharer(): FileSharer {
    return FileSharer()
}
